import { useFindAccounts } from "@/modules/financial/hooks/accounts/find-all.hook";
import { getBankIcon } from "@/modules/financial/utils/get-bank-icon";
import { PersonsSelect } from "@/modules/person/components/person-select";

import { PersonClassificationEnum, PersonTypeEnum } from "@/modules/person/enums/person-classification.enum";

import { OverlayContainer } from "@/shared/components/container/overlay-container";
import { DatePickerInput } from "@/shared/components/custom/calendar-input";
import { Button } from "@/shared/components/ui/button";
import { Input } from "@/shared/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/shared/components/ui/select";
import { zodResolver } from "@hookform/resolvers/zod";
import { motion } from "framer-motion";
import { ArrowDownCircle, ArrowUpCircle, BadgeDollarSign, Calendar, CreditCard, User } from "lucide-react";
import { useState } from "react";
import { <PERSON>, FormProvider, UseFormReturn, useForm } from "react-hook-form";
import { NumericFormat } from "react-number-format";
import { useCreateBill } from "../../hooks/bills/create.hook";
import type { CreateBillForm } from "../../validators/create-bill.form";
import { createBillSchema } from "../../validators/create-bill.form";
import { EditBillForm, editBillSchema } from "../../validators/edit-bill.form";

type FormType = CreateBillForm | EditBillForm;

interface CreateBillModalProps {
	isOpen: boolean;
	onClose: () => void;
	title?: string;
	onSubmit?: (data: FormType) => Promise<void>;
	form?: UseFormReturn<FormType>;
	isLoading?: boolean;
	isEdit?: boolean;
}

export const CreateBillModal = ({
	isOpen,
	onClose,
	title = "Nova Conta",
	onSubmit,
	form: externalForm,
	isLoading: externalLoading,
	isEdit = false,
}: CreateBillModalProps) => {
	const [loading, setLoading] = useState(false);
	const createBill = useCreateBill();
	const { accounts, isLoading: loadingAccounts } = useFindAccounts();

	const internalForm = useForm<FormType>({
		resolver: zodResolver(isEdit ? editBillSchema : createBillSchema),
		defaultValues: isEdit
			? {
					description: "",
					value: undefined,
					dueDate: undefined,
					paymentDate: undefined,
					personId: undefined,
					accountId: undefined,
				}
			: {
					type: PersonTypeEnum.Physical,
					description: "",
					value: undefined,
					dueDate: undefined,
					paymentDate: undefined,
					personId: undefined,
					accountId: undefined,
				},
	});

	const form = externalForm || internalForm;

	const { register, handleSubmit, formState, reset, control, watch } = form;
	const { errors } = formState;

	const handleInternalSubmit = async (data: FormType) => {
		setLoading(true);
		try {
			if (!isEdit) {
				await createBill.mutateAsync({
					type: (data as CreateBillForm).type,
					description: data.description,
					value: data.value,
					dueDate: data.dueDate
						? (() => {
								const [day, month, year] = data.dueDate.split("/");
								return new Date(Number(year), Number(month) - 1, Number(day)).toISOString();
							})()
						: undefined,
					paymentDate: data.paymentDate
						? (() => {
								const [day, month, year] = data.paymentDate.split("/");
								return new Date(Number(year), Number(month) - 1, Number(day)).toISOString();
							})()
						: undefined,
					personId: data.personId,
					accountId: data.accountId,
				});
			}
			reset();
			onClose();
		} finally {
			setLoading(false);
		}
	};

	const handleFormSubmit = onSubmit || handleInternalSubmit;
	const isLoading = externalLoading || loading || createBill.isPending;

	const handleReset = () => {
		reset();
		onClose();
	};

	return (
		<OverlayContainer isVisible={isOpen} onClose={handleReset}>
			<motion.div
				onClick={e => e.stopPropagation()}
				initial={{ opacity: 0, scale: 0.95 }}
				animate={{ opacity: 1, scale: 1 }}
				exit={{ opacity: 0, scale: 0.95 }}
				transition={{ duration: 0.2 }}
				className="bg-white rounded-[15px] w-full max-w-md px-4 sm:px-6 relative shadow-lg overflow-hidden py-6"
			>
				<div className="flex items-center gap-3 mb-4">
					<div className="bg-mainColor/10 p-2 rounded-full">
						<BadgeDollarSign size={24} className="text-mainColor" />
					</div>
					<h2 className="text-lg font-semibold text-gray-800">{title}</h2>
				</div>

				<FormProvider {...form}>
					<form onSubmit={handleSubmit(handleFormSubmit, console.error)} className="flex flex-col gap-4">
						{!isEdit ? (
							<div>
								<label htmlFor="type" className="block text-sm font-medium text-gray-600 mb-1">
									Tipo
								</label>
								<Controller
									control={control}
									name="type"
									render={({ field }) => (
										<Select value={String(field.value)} onValueChange={value => field.onChange(Number(value))}>
											<SelectTrigger id="type" className="w-full h-[45px] rounded-[10px]">
												<SelectValue placeholder="Selecione o tipo">
													{field.value !== undefined && (
														<span className="flex items-center gap-2">
															{field.value === PersonTypeEnum.Legal ? (
																<>
																	<ArrowDownCircle size={18} className="text-red-600" />
																	<span className="text-red-600 font-medium">A pagar</span>
																</>
															) : (
																<>
																	<ArrowUpCircle size={18} className="text-green-600" />
																	<span className="text-green-600 font-medium">A receber</span>
																</>
															)}
														</span>
													)}
												</SelectValue>
											</SelectTrigger>
											<SelectContent className="z-[100000]">
												<SelectItem key="expense" value={String(PersonTypeEnum.Legal)}>
													<div className="flex items-center gap-2 text-red-600">
														<ArrowDownCircle size={18} />
														<span>Despesa</span>
														<div className="ml-2 px-2 py-0.5 rounded-full bg-red-100 text-red-600 text-xs">A pagar</div>
														{field.value === PersonTypeEnum.Legal && (
															<span className="ml-2 text-xs font-bold">(Selecionado)</span>
														)}
													</div>
												</SelectItem>
												<SelectItem key="revenue" value={String(PersonTypeEnum.Physical)}>
													<div className="flex items-center gap-2 text-green-600">
														<ArrowUpCircle size={18} />
														<span>Receita</span>
														<div className="ml-2 px-2 py-0.5 rounded-full bg-green-100 text-green-600 text-xs">
															A receber
														</div>
														{field.value === PersonTypeEnum.Physical && (
															<span className="ml-2 text-xs font-bold">(Selecionado)</span>
														)}
													</div>
												</SelectItem>
											</SelectContent>
										</Select>
									)}
								/>
								{!isEdit && "type" in errors && errors.type && (
									<span className="text-xs text-red-500 mt-1 block">{errors.type.message}</span>
								)}
							</div>
						) : (
							<div>
								<label className="block text-sm font-medium text-gray-600 mb-1">Tipo</label>
								<div className="flex items-center gap-2">
									{(() => {
										const value = watch("type");
										if (value === PersonTypeEnum.Legal) {
											return (
												<>
													<ArrowDownCircle size={18} className="text-red-600" />
													<span className="text-red-600 font-medium">A pagar</span>
												</>
											);
										}
										return (
											<>
												<ArrowUpCircle size={18} className="text-green-600" />
												<span className="text-green-600 font-medium">A receber</span>
											</>
										);
									})()}
								</div>
							</div>
						)}

						<div>
							<label htmlFor="description" className="block text-sm font-medium text-gray-600 mb-1">
								Descrição
							</label>
							<Input
								id="description"
								type="text"
								placeholder="Ex: Pagamento de fornecedor"
								{...register("description")}
								className="w-full h-[45px] rounded-[10px]"
								autoFocus
							/>
							{errors.description && <span className="text-xs text-red-500 mt-1 block">{errors.description.message}</span>}
						</div>

						<div>
							<label htmlFor="value" className="block text-sm font-medium text-gray-600 mb-1">
								Valor
							</label>
							<div className="relative">
								<Controller
									control={control}
									name="value"
									render={({ field }) => (
										<NumericFormat
											value={field.value ?? ""}
											onValueChange={values => field.onChange(values.floatValue ?? undefined)}
											thousandSeparator="."
											decimalSeparator=","
											prefix="R$ "
											decimalScale={2}
											fixedDecimalScale
											placeholder="R$ 0,00"
											customInput={Input}
											className={`w-full h-[45px] rounded-[10px] pl-10 ${
												!isEdit && (watch("type") as PersonTypeEnum) === PersonTypeEnum.Legal
													? "text-red-600"
													: "text-green-600"
											}`}
										/>
									)}
								/>
								<BadgeDollarSign className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
								{errors.value && <span className="text-xs text-red-500 mt-1 block">{errors.value.message}</span>}
							</div>
						</div>

						<div>
							<div className="relative">
								<Controller
									control={control}
									name="dueDate"
									render={({ field }) => (
										<DatePickerInput
											className="w-full z-[100000]"
											field={field}
											inputDateClassName="w-full h-[45px] pl-10 rounded-[10px] border-gray-300"
											placeholder="Data de vencimento"
											label="Data de Vencimento"
											calendarClassName="z-[10050]"
										/>
									)}
								/>
								<Calendar className="absolute left-3 top-[47px] -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
								{errors.dueDate && <span className="text-xs text-red-500 mt-1 block">{errors.dueDate.message}</span>}
							</div>
						</div>

						<div>
							<div className="relative">
								<Controller
									control={control}
									name="paymentDate"
									render={({ field }) => (
										<DatePickerInput
											className="w-full z-[100000]"
											field={field}
											inputDateClassName="w-full h-[45px] pl-10 rounded-[10px] border-gray-300"
											placeholder="Data de pagamento"
											label="Data de Pagamento"
											calendarClassName="z-[10050]"
										/>
									)}
								/>
								<Calendar className="absolute left-3 top-[47px] -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
								{errors.paymentDate && <span className="text-xs text-red-500 mt-1 block">{errors.paymentDate.message}</span>}
							</div>
						</div>

						<div>
							<label htmlFor="personId" className="block text-sm font-medium text-gray-600 mb-1">
								Pessoa
							</label>
							<div className="relative">
								<Controller
									control={control}
									name="personId"
									render={({ field, fieldState }) => (
										<PersonsSelect
											value={field.value ? String(field.value) : undefined}
											onChange={value => field.onChange(Number(value))}
											error={fieldState.error?.message}
											icon={<User size={14} />}
											label="Pessoa"
											classification={!isEdit ? (watch("type") as PersonClassificationEnum) : undefined}
										/>
									)}
								/>
								{errors.personId && <span className="text-xs text-red-500 mt-1 block">{errors.personId.message}</span>}
							</div>
						</div>

						<div>
							<label htmlFor="account-select" className="block text-sm font-medium text-gray-600 mb-1">
								Conta
							</label>
							<div className="relative">
								<Controller
									control={control}
									name="accountId"
									render={({ field }) => (
										<Select
											value={field.value ? String(field.value) : undefined}
											onValueChange={value => field.onChange(Number(value))}
											disabled={loadingAccounts || !accounts}
										>
											<SelectTrigger id="account-select" className="w-full h-[45px] rounded-[10px] pl-10">
												<SelectValue placeholder={loadingAccounts ? "Carregando contas..." : "Selecione uma conta"} />
											</SelectTrigger>
											<SelectContent className="z-[100000]">
												{accounts?.map(account => (
													<SelectItem key={account.id} value={String(account.id)}>
														<span className="flex items-center gap-2">
															{getBankIcon(account.name)}
															{account.name}
														</span>
													</SelectItem>
												))}
											</SelectContent>
										</Select>
									)}
								/>
								<CreditCard className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
								{errors.accountId && <span className="text-xs text-red-500 mt-1 block">{errors.accountId.message}</span>}
							</div>
						</div>

						<div className="flex justify-end gap-3 mt-2">
							<Button type="button" onClick={handleReset} className="bg-gray-200 hover:bg-gray-300 text-gray-700">
								Cancelar
							</Button>
							<Button type="submit" className="bg-mainColor text-white hover:bg-mainColor/90" disabled={isLoading}>
								{isLoading ? "Salvando..." : onSubmit ? "Atualizar" : "Criar"}
							</Button>
						</div>
					</form>
				</FormProvider>
			</motion.div>
		</OverlayContainer>
	);
};
